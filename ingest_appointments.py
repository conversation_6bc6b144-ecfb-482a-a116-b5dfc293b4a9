import os
import psycopg2
from urllib.parse import urlparse
from dotenv import load_dotenv
import pandas as pd

# Load environment variables
load_dotenv()
db_url = os.getenv("SUPABASE_DB_URL")

if not db_url:
    raise EnvironmentError("SUPABASE_DB_URL not found in environment variables")

print("🔗 Connecting to Supabase database...")

# Parse connection URI
parsed = urlparse(db_url)
conn = psycopg2.connect(
    dbname=parsed.path[1:],  # Remove leading slash
    user=parsed.username,
    password=parsed.password,
    host=parsed.hostname,
    port=parsed.port
)

print("✅ Connected to database")

# Ask user about clearing existing data
print("\n🗑️  Do you want to clear existing data and re-import all appointments?")
response = input("   Clear and re-import? (y/N): ").strip().lower()

if response in ['y', 'yes']:
    print("🔄 Clearing existing data...")
    with conn.cursor() as cur:
        cur.execute("DELETE FROM rethinkDump;")
        conn.commit()
    print("✅ Existing data cleared")
else:
    print("ℹ️  Keeping existing data, will add new appointments")

# Find and load Excel file
import glob

# Look for any Excel files in the current directory
excel_files = glob.glob("*.xlsx")

if not excel_files:
    raise FileNotFoundError("No Excel files found in current directory. Please run download_simple.py first.")

# Use the most recent Excel file
excel_file = max(excel_files, key=os.path.getmtime)
print(f"📁 Found {len(excel_files)} Excel file(s), using: {excel_file}")

print(f"\n📊 Loading Excel file: {excel_file}")

# Load Excel file - skip the empty first row
df = pd.read_excel(excel_file, skiprows=1)
print(f"📈 Loaded {len(df)} rows from Excel")

# Column mapping (same as before)
COLUMN_MAPPING = {
    'Appointment Type': 'appointmentType',
    'Appointment Tag': 'appointmentTag', 
    'Service Line': 'serviceLine',
    'Service': 'service',
    'Appointment Location': 'appointmentLocation',
    'Duration': 'duration',
    'Day': 'day',
    'Date': 'date',
    'Time': 'time',
    'Scheduled Date': 'scheduledDate',
    'Modified Date': 'modifiedDate',
    'Client': 'client',
    'Staff Member': 'staff',
    'Status': 'status',
    'Session Note': 'sessionNote',
    'Staff Verification': 'staffVerification',
    'Staff Verification Address': 'staffVerificationAddress',
    'Guardian Verification': 'guardianVerification',
    'Parent Verification Address': 'parentVerificationAddress',
    'PayCode Name': 'paycodeName',
    'PayCode': 'paycode',
    'Notes': 'notes',
    'Appointment ID': 'appointmentID',
    'Validation': 'validation',
    'Place of Service': 'placeOfService'
}

def map_excel_to_db_columns(df):
    """Map Excel columns to database columns."""
    mapped_columns = {}
    for excel_col in df.columns:
        if excel_col in COLUMN_MAPPING:
            db_col = COLUMN_MAPPING[excel_col]
            mapped_columns[db_col] = excel_col
    return mapped_columns

def prepare_row_data(row, column_mapping):
    """Prepare a single row for database insertion."""
    db_columns = [
        'appointmentType', 'appointmentTag', 'serviceLine', 'service',
        'appointmentLocation', 'duration', 'day', 'date', 'time',
        'scheduledDate', 'modifiedDate', 'client', 'staff', 'status',
        'sessionNote', 'staffVerification', 'staffVerificationAddress',
        'guardianVerification', 'parentVerificationAddress',
        'paycodeName', 'paycode', 'notes', 'appointmentID',
        'validation', 'placeOfService'
    ]
    
    values = []
    for db_col in db_columns:
        if db_col in column_mapping:
            excel_col = column_mapping[db_col]
            value = row[excel_col]
            if pd.isna(value):
                values.append(None)
            else:
                values.append(value)
        else:
            values.append(None)
    
    return values

# Insert all data (including duplicates)
print("\n🔄 Mapping columns and inserting data...")
column_mapping = map_excel_to_db_columns(df)

success_count = 0
error_count = 0

with conn.cursor() as cur:
    for index, row in df.iterrows():
        try:
            values = prepare_row_data(row, column_mapping)
            
            # Insert without conflict handling - allows duplicates
            cur.execute("""
                INSERT INTO rethinkDump (
                    appointmentType, appointmentTag, serviceLine, service,
                    appointmentLocation, duration, day, date, time,
                    scheduledDate, modifiedDate, client, staff, status,
                    sessionNote, staffVerification, staffVerificationAddress,
                    guardianVerification, parentVerificationAddress,
                    paycodeName, paycode, notes, appointmentID,
                    validation, placeOfService
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s
                )
            """, values)
            
            success_count += 1
            
            if (index + 1) % 100 == 0:
                print(f"📈 Processed {index + 1}/{len(df)} rows...")
                
        except Exception as e:
            error_count += 1
            print(f"❌ Error processing row {index + 1}: {e}")
            continue

conn.commit()
conn.close()

print(f"\n🎉 Data upload completed!")
print(f"✅ Successfully inserted: {success_count} rows")
print(f"❌ Errors encountered: {error_count} rows")
print(f"📊 Total processed: {len(df)} rows")
print(f"\n📋 Your Supabase table now has all {success_count} appointment instances!")
print(f"   ✅ All data ingested successfully, including recurring series appointments")
