#!/usr/bin/env python3
"""
Download Rethink BH appointments → Excel (direct Excel download - SIMPLE VERSION)
Uses the GetAppointmentsListPrintAsync endpoint that returns Excel directly.
"""

import os, requests, base64
from dotenv import load_dotenv

# ── Config ────────────────────────────────────────────────────────────────────
load_dotenv()
EMAIL    = os.getenv("RTHINK_USER")
PASSWORD = os.getenv("RTHINK_PASS")
if not EMAIL or not PASSWORD:
    raise EnvironmentError("Add RTHINK_USER and RTHINK_PASS to .env")

BASE = "https://webapp.rethinkbehavioralhealth.com"
HEADERS = {
    "Content-Type": "application/json;charset=utf-8",
    "Accept": "application/json, text/plain, */*",
    "X-Application-Key": "74569e11-18b4-4122-a58d-a4b830aa12c4",
    "X-Origin": "Ang<PERSON>",
    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64; rv:139.0) Gecko/139.0",
    "Origin": BASE,
    "Referer": f"{BASE}/Healthcare#/Login",
}

s = requests.Session()

# ── Helpers ──────────────────────────────────────────────────────────────────
def _fetch_token() -> str | None:
    for c in s.cookies:
        if any(k in c.name.upper() for k in ("XSRF", "ANTIFORGERY", "REQUESTVERIFICATIONTOKEN")):
            return c.value
    return None

def _with_token(hdr: dict) -> dict:
    tok = _fetch_token()
    if not tok:
        raise RuntimeError("No anti-forgery token present in cookies")
    return {**hdr, "X-XSRF-TOKEN": tok}

def download_excel_directly(payload: dict, outfile: str) -> None:
    """
    Download Excel file directly from GetAppointmentsListPrintAsync endpoint.
    This endpoint returns the Excel file directly with all your filtered data.
    """
    print("📥  Downloading Excel file directly from API...")
    
    # Create payload for Excel download - remove pagination to get all data
    payload_clean = payload.copy()
    payload_clean.pop("skip", None)
    payload_clean.pop("pageSize", None)
    payload_clean["sameLocationStaffIds"] = []  # Required field
    
    # Ensure filterOptions has the same filters as the main payload
    if "filterOptions" in payload_clean:
        payload_clean["filterOptions"]["memberIds"] = payload_clean.get("memberIds", [])
        payload_clean["filterOptions"]["staffIds"] = payload_clean.get("staffIds", [])
    
    print(f"📋  Filtering by {len(payload_clean.get('memberIds', []))} members and {len(payload_clean.get('staffIds', []))} staff")
    
    r = s.post(
        f"{BASE}/core/api/scheduling/scheduling/GetAppointmentsListPrintAsync",
        json=payload_clean,
        headers=_with_token(HEADERS),
        timeout=120,
    )
    
    if r.status_code != 200:
        print(f"❌  Excel download failed — {r.status_code}")
        print(r.text[:1000])
        r.raise_for_status()
    
    # Handle the response
    content_type = r.headers.get('content-type', '').lower()
    print(f"📋  Response content-type: {content_type}")
    
    if 'spreadsheet' in content_type or 'excel' in content_type:
        # Direct binary Excel file
        with open(outfile, 'wb') as f:
            f.write(r.content)
        print(f"✅  Downloaded Excel file directly → {outfile}")
    else:
        # Try base64 decoding if needed
        try:
            excel_data = base64.b64decode(r.text)
            with open(outfile, 'wb') as f:
                f.write(excel_data)
            print(f"✅  Downloaded Excel file (base64 decoded) → {outfile}")
        except Exception as e:
            print(f"❌  Unexpected response format: {content_type}")
            raise
    
    # Show file info
    file_size = os.path.getsize(outfile)
    print(f"📊  File size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
    print(f"🎯  Excel file contains your filtered appointment data!")

# ── Authentication ────────────────────────────────────────────────────────────
print("🔐  Authenticating...")
s.get(f"{BASE}/HealthCare", headers=HEADERS).raise_for_status()
s.post(f"{BASE}/HealthCare/SingleSignOn/GetAuthenticationDetail",
       json={"User": EMAIL}, headers=_with_token(HEADERS)).raise_for_status()
print("✅  Email verified")
s.post(f"{BASE}/HealthCare/User/Login",
       json={"User": EMAIL, "Password": PASSWORD, "setPermissions": True},
       headers=_with_token(HEADERS)).raise_for_status()
print("✅  Logged in")
s.get(f"{BASE}/core/scheduler/appointments",
      headers=_with_token(HEADERS)).raise_for_status()
print("✅  Authentication complete")

# ── Data Configuration (Browser-compliant for 217 rows) ─────────────────────
# Use browser's exact date format for maximum compatibility
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

# Get end of current month
end_date = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0) + relativedelta(months=1, days=-1)
# Get date 6 months before
start_date = end_date - relativedelta(months=6)

START = start_date.strftime("%-m/%-d/%Y, %I:%M:%S %p")
END = end_date.strftime("%-m/%-d/%Y, %I:%M:%S %p")

# For browser compliance (217 rows), we use empty member/staff arrays
# This matches exactly what the browser sends for the Excel download
MEMBER_IDS = []  # Empty for browser compliance
STAFF_IDS = []   # Empty for browser compliance

# ── Payload Structure ────────────────────────────────────────────────────────
payload = {
    "startDate": START,
    "endDate": END,
    "checkExceedAuthorization": True,
    "showEVV": 0,
    "memberIds": MEMBER_IDS,
    "clientIds": [],
    "staffIds": STAFF_IDS,
    "timeFormat": "hh:mm tt",
    "dateFormat": "MM/dd/yyyy",
    "includeAssignedOnly": False,
    "sorting": {
        "field": "date",
        "asc": False,
        "type": 1
    },
    "filterOptions": {
        "startDate": START,
        "endDate": END,
        "appointmentStatus": [
            "Scheduled", "Needs Verification", "Completed",
            "Cancelled - Needs Reschedule", "Cancelled - Rescheduled",
            "Cancelled - Cannot Reschedule"
        ],
        "billedStatus": [],
        "sessionNoteStatus": [],
        "sessionNoteAlerts": [],
        "appointmentLocation": [],
        "location": [],
        "appointmentType": [],
        "payableTypes": [],
        "authRequirements": [],
        "CMFDate": None,
        "CMTDate": None,
        "appointmentRequirements": ["staffVerification"],
        "authorizationType": [],
        "appointmentId": None,
        "authorizationNumber": "",
        "billingCodes": None,
        "acknowledgeableExceptions": [],
        "EVVReasonCodes": [],
        "EVVStatus": [],
        "missingClockedIn": None,
        "missingClockedOut": None,
        "renderingProviders": [],
        "service": [],
        "serviceLineId": 6476,
        "showEVV": False,
        "memberIds": MEMBER_IDS,  # Empty for browser compliance
        "clientIds": [],
        "staffIds": STAFF_IDS,    # Empty for browser compliance
        "validations": [],
        "clientsOptions": [],
    },
    "schedulerPermissionLevelTypeId": 2,
}

# ── Download Excel (Browser-compliant) ──────────────────────────────────────
outfile = f"appointments_{start_date.strftime('%B_%Y')}_to_{end_date.strftime('%B_%Y')}.xlsx"

print("\n🚀  Starting Excel download...")
download_excel_directly(payload, outfile)

print("\n🎉  Download completed!")
print(f"   • File: {outfile}")
print(f"   • Date range: {start_date.strftime('%B %d, %Y')} to {end_date.strftime('%B %d, %Y')}")
