#!/usr/bin/env python3
"""
Download Rethink BH appointments → Excel (direct Excel download - SIMPLE VERSION)
Uses the GetAppointmentsListPrintAsync endpoint that returns Excel directly.
"""

import os, requests, base64
from dotenv import load_dotenv

# ── Config ────────────────────────────────────────────────────────────────────
load_dotenv()
EMAIL    = os.getenv("RTHINK_USER")
PASSWORD = os.getenv("RTHINK_PASS")
if not EMAIL or not PASSWORD:
    raise EnvironmentError("Add RTHINK_USER and RTHINK_PASS to .env")

BASE = "https://webapp.rethinkbehavioralhealth.com"
HEADERS = {
    "Content-Type": "application/json;charset=utf-8",
    "Accept": "application/json, text/plain, */*",
    "X-Application-Key": "74569e11-18b4-4122-a58d-a4b830aa12c4",
    "X-Origin": "Ang<PERSON>",
    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64; rv:139.0) Gecko/139.0",
    "Origin": BASE,
    "Referer": f"{BASE}/Healthcare#/Login",
}

s = requests.Session()

# ── Helpers ──────────────────────────────────────────────────────────────────
def _fetch_token() -> str | None:
    for c in s.cookies:
        if any(k in c.name.upper() for k in ("XSRF", "ANTIFORGERY", "REQUESTVERIFICATIONTOKEN")):
            return c.value
    return None

def _with_token(hdr: dict) -> dict:
    tok = _fetch_token()
    if not tok:
        raise RuntimeError("No anti-forgery token present in cookies")
    return {**hdr, "X-XSRF-TOKEN": tok}

def download_excel_directly(payload: dict, outfile: str) -> None:
    """
    Download Excel file directly from GetAppointmentsListPrintAsync endpoint.
    This endpoint returns the Excel file directly with all your filtered data.
    """
    print("📥  Downloading Excel file directly from API...")
    
    # Create payload for Excel download - remove pagination to get all data
    payload_clean = payload.copy()
    payload_clean.pop("skip", None)
    payload_clean.pop("pageSize", None)
    payload_clean["sameLocationStaffIds"] = []  # Required field
    
    # Ensure filterOptions has the same filters as the main payload
    if "filterOptions" in payload_clean:
        payload_clean["filterOptions"]["memberIds"] = payload_clean.get("memberIds", [])
        payload_clean["filterOptions"]["staffIds"] = payload_clean.get("staffIds", [])
    
    print(f"📋  Filtering by {len(payload_clean.get('memberIds', []))} members and {len(payload_clean.get('staffIds', []))} staff")
    
    r = s.post(
        f"{BASE}/core/api/scheduling/scheduling/GetAppointmentsListPrintAsync",
        json=payload_clean,
        headers=_with_token(HEADERS),
        timeout=120,
    )
    
    if r.status_code != 200:
        print(f"❌  Excel download failed — {r.status_code}")
        print(r.text[:1000])
        r.raise_for_status()
    
    # Handle the response
    content_type = r.headers.get('content-type', '').lower()
    print(f"📋  Response content-type: {content_type}")
    
    if 'spreadsheet' in content_type or 'excel' in content_type:
        # Direct binary Excel file
        with open(outfile, 'wb') as f:
            f.write(r.content)
        print(f"✅  Downloaded Excel file directly → {outfile}")
    else:
        # Try base64 decoding if needed
        try:
            excel_data = base64.b64decode(r.text)
            with open(outfile, 'wb') as f:
                f.write(excel_data)
            print(f"✅  Downloaded Excel file (base64 decoded) → {outfile}")
        except Exception as e:
            print(f"❌  Unexpected response format: {content_type}")
            raise
    
    # Show file info
    file_size = os.path.getsize(outfile)
    print(f"📊  File size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
    print(f"🎯  Excel file contains your filtered appointment data!")

# ── Authentication ────────────────────────────────────────────────────────────
print("🔐  Authenticating...")
s.get(f"{BASE}/HealthCare", headers=HEADERS).raise_for_status()
s.post(f"{BASE}/HealthCare/SingleSignOn/GetAuthenticationDetail",
       json={"User": EMAIL}, headers=_with_token(HEADERS)).raise_for_status()
print("✅  Email verified")
s.post(f"{BASE}/HealthCare/User/Login",
       json={"User": EMAIL, "Password": PASSWORD, "setPermissions": True},
       headers=_with_token(HEADERS)).raise_for_status()
print("✅  Logged in")
s.get(f"{BASE}/core/scheduler/appointments",
      headers=_with_token(HEADERS)).raise_for_status()
print("✅  Authentication complete")

# ── Data Configuration ───────────────────────────────────────────────────────
START = "6/12/2025, 3:00:00 AM"
END   = "6/19/2025, 3:00:00 AM"

MEMBER_IDS = [
    538160, 538960, 558267, 563649, 563708, 583650, 584496, 587576,
    599247, 623283, 628487, 631789, 642691, 647215, 649209, 659401,
    677789, 715312, 715313, 732653, 742072, 745710, 785137, 797141,
    797147, 800951, 818463, 818464, 818465, 840574, 851785, 862597,
    862598, 875206, 898539, 898540, 901333, 909360, 915659, 924605,
    931706
]

STAFF_IDS = [
    207014, 207378, 217423, 220417, 220443, 231732, 232218, 234101,
    240153, 252207, 254937, 256827, 262435, 264792, 265890, 271750,
    281251, 301757, 301758, 311385, 316287, 318261, 341594, 347881,
    347886, 349815, 359039, 359040, 359041, 368701, 375034, 381113,
    381114, 388710, 402051, 402052, 403689, 408177, 411746, 416840,
    421072
]

# ── Payload Structure ────────────────────────────────────────────────────────
payload = {
    "startDate": START,
    "endDate": END,
    "checkExceedAuthorization": True,
    "showEVV": 0,
    "memberIds": MEMBER_IDS,
    "clientIds": [],
    "staffIds": STAFF_IDS,
    "timeFormat": "hh:mm tt",
    "dateFormat": "MM/dd/yyyy",
    "includeAssignedOnly": False,
    "sorting": {
        "field": "date",
        "asc": False,
        "type": 1
    },
    "filterOptions": {
        "startDate": START,
        "endDate": END,
        "appointmentStatus": [
            "Scheduled", "Needs Verification", "Completed",
            "Cancelled - Needs Reschedule", "Cancelled - Rescheduled",
            "Cancelled - Cannot Reschedule"
        ],
        "billedStatus": [],
        "sessionNoteStatus": [],
        "sessionNoteAlerts": [],
        "appointmentLocation": [],
        "location": [],
        "appointmentType": [],
        "payableTypes": [],
        "authRequirements": [],
        "CMFDate": None,
        "CMTDate": None,
        "appointmentRequirements": ["staffVerification"],
        "authorizationType": [],
        "appointmentId": None,
        "authorizationNumber": "",
        "billingCodes": None,
        "acknowledgeableExceptions": [],
        "EVVReasonCodes": [],
        "EVVStatus": [],
        "missingClockedIn": None,
        "missingClockedOut": None,
        "renderingProviders": [],
        "service": [],
        "serviceLineId": 6476,
        "showEVV": False,
        "memberIds": MEMBER_IDS,  # Include in filterOptions too
        "clientIds": [],
        "staffIds": STAFF_IDS,
        "validations": [],
        "clientsOptions": [],
    },
    "schedulerPermissionLevelTypeId": 2,
}

# ── Download Excel (Two Options) ────────────────────────────────────────────
print("\n🚀  Starting Excel downloads...")

# Option 1: Filtered data (179 rows) - your specific members/staff
print("\n📋  Option 1: Downloading FILTERED data (your specific members/staff)...")
download_excel_directly(payload, "appointments_filtered_179_rows.xlsx")

# Option 2: All data (217 rows) - like browser download without filters
print("\n📋  Option 2: Downloading ALL data (like browser without filters)...")
payload_all = payload.copy()
payload_all["memberIds"] = []
payload_all["staffIds"] = []
payload_all["startDate"] = "6/12/2025, 12:00:00 AM"
payload_all["endDate"] = "6/19/2025, 12:00:00 AM"
if "filterOptions" in payload_all:
    payload_all["filterOptions"]["memberIds"] = []
    payload_all["filterOptions"]["staffIds"] = []
    payload_all["filterOptions"]["startDate"] = "6/12/2025, 12:00:00 AM"
    payload_all["filterOptions"]["endDate"] = "6/19/2025, 12:00:00 AM"

download_excel_directly(payload_all, "appointments_all_217_rows.xlsx")

print("\n🎉  Both downloads completed!")
print("\n📋  Summary:")
print("   • appointments_filtered_179_rows.xlsx: Your filtered data (179 rows)")
print("   • appointments_all_217_rows.xlsx: All appointments (217 rows)")
print("   • Date range: June 12-19, 2025")
print("   • Choose the file that matches your needs!")
