#!/usr/bin/env python3
"""
Reset auto-increment IDs in the rethinkDump table.
This will renumber all records starting from 1.
"""

import os
import psycopg2
from urllib.parse import urlparse
from dotenv import load_dotenv

# Load environment variables
load_dotenv()
db_url = os.getenv("SUPABASE_DB_URL")

if not db_url:
    raise EnvironmentError("SUPABASE_DB_URL not found in environment variables")

print("🔗 Connecting to Supabase database...")

# Parse connection URI
parsed = urlparse(db_url)
conn = psycopg2.connect(
    dbname=parsed.path[1:],  # Remove leading slash
    user=parsed.username,
    password=parsed.password,
    host=parsed.hostname,
    port=parsed.port
)

print("✅ Connected to database")

def reset_auto_increment_ids():
    """Reset the auto-increment IDs to start from 1."""
    
    with conn.cursor() as cur:
        # First, check current state
        cur.execute("SELECT COUNT(*) FROM rethinkDump;")
        total_rows = cur.fetchone()[0]
        
        cur.execute("SELECT MIN(id), MAX(id) FROM rethinkDump;")
        min_id, max_id = cur.fetchone()
        
        print(f"📊 Current state:")
        print(f"   • Total rows: {total_rows}")
        print(f"   • ID range: {min_id} to {max_id}")
        
        if total_rows == 0:
            print("⚠️  Table is empty, nothing to reset")
            return
        
        # Get the sequence name for the id column
        cur.execute("""
            SELECT pg_get_serial_sequence('rethinkdump', 'id');
        """)
        sequence_name = cur.fetchone()[0]
        
        if not sequence_name:
            print("❌ No auto-increment sequence found for 'id' column")
            return
            
        print(f"🔍 Found sequence: {sequence_name}")
        
        # Method 1: Add a temporary column, copy data with new IDs, then replace
        print("\n🔄 Resetting IDs...")
        print("   Step 1: Adding temporary ID column...")
        
        # Add temporary column
        cur.execute("ALTER TABLE rethinkDump ADD COLUMN temp_id SERIAL;")
        
        print("   Step 2: Dropping old ID column...")
        # Drop the old id column
        cur.execute("ALTER TABLE rethinkDump DROP COLUMN id;")
        
        print("   Step 3: Renaming temp column to id...")
        # Rename temp_id to id
        cur.execute("ALTER TABLE rethinkDump RENAME COLUMN temp_id TO id;")
        
        print("   Step 4: Setting id as primary key...")
        # Make it the primary key
        cur.execute("ALTER TABLE rethinkDump ADD PRIMARY KEY (id);")
        
        # Commit the changes
        conn.commit()
        
        # Check the new state
        cur.execute("SELECT COUNT(*) FROM rethinkDump;")
        total_rows_after = cur.fetchone()[0]
        
        cur.execute("SELECT MIN(id), MAX(id) FROM rethinkDump;")
        min_id_after, max_id_after = cur.fetchone()
        
        print(f"\n✅ ID reset completed!")
        print(f"📊 New state:")
        print(f"   • Total rows: {total_rows_after}")
        print(f"   • ID range: {min_id_after} to {max_id_after}")
        print(f"   • IDs are now sequential from 1 to {total_rows_after}")

def main():
    """Main function."""
    print("🔄 Rethink BH - Reset Auto-Increment IDs")
    print("=" * 50)
    
    try:
        reset_auto_increment_ids()
        print("\n🎉 ID reset completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error resetting IDs: {e}")
        conn.rollback()
        raise
    
    finally:
        conn.close()
        print("🔌 Database connection closed")

if __name__ == "__main__":
    main()
