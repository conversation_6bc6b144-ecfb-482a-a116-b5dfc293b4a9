#!/usr/bin/env python3
"""
Rethink BH Automation - Complete Workflow
Downloads appointments and ingests them into Supabase in one command.
"""

import subprocess
import sys
import os
from datetime import datetime

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🚀 {description}")
    print(f"   Command: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Error: {e}")
        if e.stdout:
            print(f"   Output: {e.stdout}")
        if e.stderr:
            print(f"   Error output: {e.stderr}")
        return False

def check_env_file():
    """Check if .env file exists and has required variables."""
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("   Please create a .env file with:")
        print("   RTHINK_USER=<EMAIL>")
        print("   RTHINK_PASS=your_password")
        print("   SUPABASE_DB_URL=postgresql://user:pass@host:port/db")
        return False
    
    print("✅ .env file found")
    return True

def main():
    """Main automation workflow."""
    print("🎯 Rethink BH Automation - Complete Workflow")
    print("=" * 50)
    
    # Check prerequisites
    if not check_env_file():
        sys.exit(1)
    
    # Ask user what they want to do
    print("\nWhat would you like to do?")
    print("1. Download appointments only")
    print("2. Ingest existing Excel file only") 
    print("3. Download and ingest (complete workflow)")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == "1":
        # Download only
        success = run_command("uv run python download_simple.py", "Downloading appointments")
        if success:
            print(f"\n🎉 Download completed! Check for the Excel file.")
    
    elif choice == "2":
        # Ingest only
        success = run_command("uv run python modify_schema_and_ingest.py", "Ingesting data to Supabase")
        if success:
            print(f"\n🎉 Data ingestion completed!")
    
    elif choice == "3":
        # Complete workflow
        print(f"\n📅 Starting complete workflow at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Step 1: Download
        if run_command("uv run python download_simple.py", "Downloading appointments"):
            print("✅ Download phase completed")
            
            # Step 2: Ingest
            if run_command("uv run python modify_schema_and_ingest.py", "Ingesting data to Supabase"):
                print(f"\n🎉 Complete workflow finished successfully!")
                print(f"   ✅ Appointments downloaded")
                print(f"   ✅ Data ingested to Supabase")
                print(f"   📊 Check your Supabase table for the latest data")
            else:
                print(f"\n❌ Workflow failed at ingestion step")
                sys.exit(1)
        else:
            print(f"\n❌ Workflow failed at download step")
            sys.exit(1)
    
    else:
        print("❌ Invalid choice. Please run the script again and choose 1, 2, or 3.")
        sys.exit(1)

if __name__ == "__main__":
    main()
