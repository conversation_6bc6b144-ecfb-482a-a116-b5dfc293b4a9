import os
import psycopg2
from urllib.parse import urlparse
from dotenv import load_dotenv
import pandas as pd

# Load environment variables
load_dotenv()
db_url = os.getenv("SUPABASE_DB_URL")

if not db_url:
    raise EnvironmentError("SUPABASE_DB_URL not found in environment variables")

print("🔗 Connecting to Supabase database...")

# Parse connection URI
parsed = urlparse(db_url)
conn = psycopg2.connect(
    dbname=parsed.path[1:],  # Remove leading slash
    user=parsed.username,
    password=parsed.password,
    host=parsed.hostname,
    port=parsed.port
)

print("✅ Connected to database")

# Step 1: Modify the database schema to allow duplicate appointmentIDs
print("\n🔧 Modifying database schema...")

with conn.cursor() as cur:
    try:
        # First, let's check the current schema
        cur.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'rethinkdump'
            ORDER BY ordinal_position;
        """)
        
        current_schema = cur.fetchall()
        print("📋 Current schema:")
        for col in current_schema:
            print(f"   {col[0]}: {col[1]} (nullable: {col[2]})")
        
        # Check if we need to modify the schema
        cur.execute("""
            SELECT constraint_name, constraint_type 
            FROM information_schema.table_constraints 
            WHERE table_name = 'rethinkdump' AND constraint_type = 'PRIMARY KEY';
        """)
        
        pk_constraints = cur.fetchall()
        
        if pk_constraints:
            print(f"\n🔄 Found primary key constraint: {pk_constraints[0][0]}")
            print("   Modifying schema to allow duplicate appointmentIDs...")
            
            # Drop the primary key constraint
            cur.execute(f"ALTER TABLE rethinkDump DROP CONSTRAINT {pk_constraints[0][0]};")
            print("   ✅ Dropped primary key constraint on appointmentID")
            
            # Add a new auto-incrementing primary key
            cur.execute("ALTER TABLE rethinkDump ADD COLUMN id SERIAL PRIMARY KEY;")
            print("   ✅ Added new auto-incrementing 'id' column as primary key")
            
            conn.commit()
        else:
            print("   ℹ️  No primary key constraint found, schema is ready")
            
    except psycopg2.Error as e:
        print(f"❌ Error modifying schema: {e}")
        conn.rollback()
        raise

# Step 2: Clear existing data (optional - ask user)
print("\n🗑️  Do you want to clear existing data and re-import all appointments?")
print("   This will ensure you get all 3,894 appointments including duplicates.")
response = input("   Clear and re-import? (y/N): ").strip().lower()

if response in ['y', 'yes']:
    print("🔄 Clearing existing data...")
    with conn.cursor() as cur:
        cur.execute("DELETE FROM rethinkDump;")
        conn.commit()
    print("✅ Existing data cleared")
else:
    print("ℹ️  Keeping existing data, will only add new appointments")

# Step 3: Find and load Excel file
excel_files = [
    "appointments_june_2025_browser_compliant.xlsx",
    "appointments_june_2025_direct.xlsx", 
    "appointments_june_2025_final.xlsx",
    "appointments_December_2024_to_June_2025.xlsx"
]

excel_file = None
for filename in excel_files:
    if os.path.exists(filename):
        excel_file = filename
        break

if not excel_file:
    raise FileNotFoundError(f"No Excel file found. Looking for: {excel_files}")

print(f"\n📊 Loading Excel file: {excel_file}")

# Load Excel file - skip the empty first row
df = pd.read_excel(excel_file, skiprows=1)
print(f"📈 Loaded {len(df)} rows from Excel")

# Column mapping (same as before)
COLUMN_MAPPING = {
    'Appointment Type': 'appointmentType',
    'Appointment Tag': 'appointmentTag', 
    'Service Line': 'serviceLine',
    'Service': 'service',
    'Appointment Location': 'appointmentLocation',
    'Duration': 'duration',
    'Day': 'day',
    'Date': 'date',
    'Time': 'time',
    'Scheduled Date': 'scheduledDate',
    'Modified Date': 'modifiedDate',
    'Client': 'client',
    'Staff Member': 'staff',
    'Status': 'status',
    'Session Note': 'sessionNote',
    'Staff Verification': 'staffVerification',
    'Staff Verification Address': 'staffVerificationAddress',
    'Guardian Verification': 'guardianVerification',
    'Parent Verification Address': 'parentVerificationAddress',
    'PayCode Name': 'paycodeName',
    'PayCode': 'paycode',
    'Notes': 'notes',
    'Appointment ID': 'appointmentID',
    'Validation': 'validation',
    'Place of Service': 'placeOfService'
}

def map_excel_to_db_columns(df):
    """Map Excel columns to database columns."""
    mapped_columns = {}
    for excel_col in df.columns:
        if excel_col in COLUMN_MAPPING:
            db_col = COLUMN_MAPPING[excel_col]
            mapped_columns[db_col] = excel_col
    return mapped_columns

def prepare_row_data(row, column_mapping):
    """Prepare a single row for database insertion."""
    db_columns = [
        'appointmentType', 'appointmentTag', 'serviceLine', 'service',
        'appointmentLocation', 'duration', 'day', 'date', 'time',
        'scheduledDate', 'modifiedDate', 'client', 'staff', 'status',
        'sessionNote', 'staffVerification', 'staffVerificationAddress',
        'guardianVerification', 'parentVerificationAddress',
        'paycodeName', 'paycode', 'notes', 'appointmentID',
        'validation', 'placeOfService'
    ]
    
    values = []
    for db_col in db_columns:
        if db_col in column_mapping:
            excel_col = column_mapping[db_col]
            value = row[excel_col]
            if pd.isna(value):
                values.append(None)
            else:
                values.append(value)
        else:
            values.append(None)
    
    return values

# Step 4: Insert all data (including duplicates)
print("\n🔄 Mapping columns and inserting data...")
column_mapping = map_excel_to_db_columns(df)

success_count = 0
error_count = 0

with conn.cursor() as cur:
    for index, row in df.iterrows():
        try:
            values = prepare_row_data(row, column_mapping)
            
            # Insert without conflict handling - allows duplicates
            cur.execute("""
                INSERT INTO rethinkDump (
                    appointmentType, appointmentTag, serviceLine, service,
                    appointmentLocation, duration, day, date, time,
                    scheduledDate, modifiedDate, client, staff, status,
                    sessionNote, staffVerification, staffVerificationAddress,
                    guardianVerification, parentVerificationAddress,
                    paycodeName, paycode, notes, appointmentID,
                    validation, placeOfService
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s
                )
            """, values)
            
            success_count += 1
            
            if (index + 1) % 100 == 0:
                print(f"📈 Processed {index + 1}/{len(df)} rows...")
                
        except Exception as e:
            error_count += 1
            print(f"❌ Error processing row {index + 1}: {e}")
            continue

conn.commit()
conn.close()

print(f"\n🎉 Data upload completed!")
print(f"✅ Successfully inserted: {success_count} rows")
print(f"❌ Errors encountered: {error_count} rows")
print(f"📊 Total processed: {len(df)} rows")
print(f"\n📋 Your Supabase table should now have all {success_count} appointment instances,")
print(f"   including recurring series appointments with the same appointmentID!")
