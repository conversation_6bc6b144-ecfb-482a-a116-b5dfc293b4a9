import pandas as pd
import os

# Find the Excel file
excel_files = [
    "appointments_june_2025_browser_compliant.xlsx",
    "appointments_june_2025_direct.xlsx", 
    "appointments_june_2025_final.xlsx",
    "appointments_December_2024_to_June_2025.xlsx"
]

excel_file = None
for filename in excel_files:
    if os.path.exists(filename):
        excel_file = filename
        break

if not excel_file:
    raise FileNotFoundError(f"No Excel file found. Looking for: {excel_files}")

print(f"📊 Analyzing Excel file: {excel_file}")

# Load Excel file - skip the empty first row
df = pd.read_excel(excel_file, skiprows=1)
print(f"📈 Total rows in Excel: {len(df)}")

# Check for duplicates in Appointment ID column
appointment_id_col = 'Appointment ID'
if appointment_id_col in df.columns:
    total_rows = len(df)
    unique_appointment_ids = df[appointment_id_col].nunique()
    duplicate_count = total_rows - unique_appointment_ids
    
    print(f"📋 Total rows: {total_rows}")
    print(f"🔑 Unique Appointment IDs: {unique_appointment_ids}")
    print(f"🔄 Duplicate Appointment IDs: {duplicate_count}")
    
    if duplicate_count > 0:
        print(f"\n✅ This explains the difference!")
        print(f"   • Excel rows processed: {total_rows}")
        print(f"   • Unique records inserted: {unique_appointment_ids}")
        print(f"   • Duplicates skipped: {duplicate_count}")
        
        # Show some examples of duplicates
        duplicates = df[df.duplicated(subset=[appointment_id_col], keep=False)]
        if len(duplicates) > 0:
            print(f"\n📄 Sample duplicate Appointment IDs:")
            duplicate_ids = duplicates[appointment_id_col].value_counts().head(5)
            for appt_id, count in duplicate_ids.items():
                print(f"   • ID {appt_id}: appears {count} times")
    else:
        print("🤔 No duplicates found in Appointment ID column")
        print("   The difference might be due to other factors")
        
else:
    print(f"❌ Column '{appointment_id_col}' not found in Excel file")
    print(f"Available columns: {df.columns.tolist()}")
