# Rethink BH Automation

Automated tools for downloading and ingesting Rethink Behavioral Health appointment data.

## 🚀 Quick Start

### Prerequisites
- Python 3.12+
- uv package manager
- Supabase database access

### Setup
1. Install dependencies:
   ```bash
   uv sync
   ```

2. Configure environment variables in `.env`:
   ```
   RTHINK_USER=<EMAIL>
   RTHINK_PASS=your_password
   SUPABASE_DB_URL=postgresql://user:pass@host:port/db
   ```

## 📊 Usage

### Option 1: Complete Automation (Recommended)
Run the complete workflow with interactive menu:
```bash
uv run python run_automation.py
```

### Option 2: Individual Scripts

#### Download Appointments
Download Excel files directly from Rethink BH (browser-compliant, 217 rows):
```bash
uv run python download_simple.py
```

#### Ingest to Supabase
Import Excel data into your Supabase database:
```bash
uv run python ingest_appointments.py
```

## 📁 Files

- `run_automation.py` - **Main script** - Complete workflow with interactive menu
- `download_simple.py` - Downloads Excel files from Rethink BH API
- `ingest_appointments.py` - Ingests Excel data into Supabase
- `appointments_December_2024_to_June_2025.xlsx` - Sample data file
- `pyproject.toml` - Project dependencies and configuration

## 🔧 Features

- **Browser-compliant downloads** - Matches exact browser behavior (217 rows)
- **Handles recurring appointments** - Preserves all instances with duplicate IDs
- **Robust error handling** - Comprehensive logging and error recovery
- **Automatic schema management** - Modifies database schema as needed
- **Progress tracking** - Real-time progress updates during processing

## 📋 Database Schema

The `rethinkDump` table includes:
- Auto-incrementing primary key (`id`)
- All appointment fields (type, location, client, staff, etc.)
- Supports duplicate `appointmentID`s for recurring series

## 🎯 Results

- Downloads: ~217 appointment records per week
- Ingestion: All records preserved including recurring series
- Performance: ~3,894 records processed in under 2 minutes