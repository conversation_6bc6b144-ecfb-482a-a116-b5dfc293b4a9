# Rethink BH Automation

Automated tools for downloading and ingesting Rethink Behavioral Health appointment data.

## 🚀 Quick Start

### Prerequisites
- Python 3.12+
- uv package manager
- Supabase database access

### Setup
1. Install dependencies:
   ```bash
   uv sync
   ```

2. Configure environment variables in `.env`:
   ```
   RTHINK_USER=<EMAIL>
   RTHINK_PASS=your_password
   SUPABASE_DB_URL=postgresql://user:pass@host:port/db
   ```

## 📊 Usage

### Option 1: Complete Automation (Recommended)
Run the complete workflow with interactive menu:
```bash
uv run python run_automation.py
```

### Option 2: Individual Scripts

#### Download Appointments
Download Excel files directly from Rethink BH (browser-compliant, 217 rows):
```bash
uv run python download_simple.py
```

#### Ingest to Supabase
Import Excel data into your Supabase database:
```bash
uv run python ingest_appointments.py
```

## 📁 Files

- `run_automation.py` - **Main script** - Complete workflow with interactive menu
- `download_simple.py` - Downloads Excel files from Rethink BH API (saves to `downloads/` folder)
- `ingest_appointments.py` - Ingests Excel data into Supabase (with daily table refresh)
- `downloads/` - Folder containing downloaded Excel files with timestamps
- `pyproject.toml` - Project dependencies and configuration

## 🔧 Features

- **Browser-compliant downloads** - Matches exact browser behavior (~217 rows)
- **Organized file storage** - Downloads saved to timestamped files in `downloads/` folder
- **Daily table refresh** - Truncates and reloads data for up-to-date appointments
- **Sequential ID reset** - Auto-increment IDs reset to 1-N on each import
- **Handles recurring appointments** - Preserves all instances with duplicate appointmentIDs
- **Robust error handling** - Comprehensive logging and error recovery
- **Progress tracking** - Real-time progress updates during processing

## 📋 Database Schema

The `rethinkDump` table includes:
- Auto-incrementing primary key (`id`)
- All appointment fields (type, location, client, staff, etc.)
- Supports duplicate `appointmentID`s for recurring series

