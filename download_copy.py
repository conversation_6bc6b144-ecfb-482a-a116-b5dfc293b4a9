#!/usr/bin/env python3
"""
Download Rethink BH appointments → Excel (direct Excel download)
Uses the GetAppointmentsListPrintAsync endpoint that returns <PERSON>cel directly.
"""

import os, requests, base64
from dotenv import load_dotenv

# ── Config ────────────────────────────────────────────────────────────────────
load_dotenv()
EMAIL    = os.getenv("RTHINK_USER")
PASSWORD = os.getenv("RTHINK_PASS")
if not EMAIL or not PASSWORD:
    raise EnvironmentError("Add RTHINK_USER and RTHINK_PASS to .env")

BASE = "https://webapp.rethinkbehavioralhealth.com"
HEADERS = {
    "Content-Type": "application/json;charset=utf-8",
    "Accept": "application/json, text/plain, */*",
    "X-Application-Key": "74569e11-18b4-4122-a58d-a4b830aa12c4",
    "X-Origin": "Angular",
    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64; rv:139.0) Gecko/139.0",
    "Origin": BASE,
    "Referer": f"{BASE}/Healthcare#/Login",
}

s = requests.Session()

# ── Helpers ──────────────────────────────────────────────────────────────────
def _fetch_token() -> str | None:
    for c in s.cookies:
        if any(k in c.name.upper() for k in ("XSRF", "ANTIFORGERY", "REQUESTVERIFICATIONTOKEN")):
            return c.value
    return None

def _with_token(hdr: dict) -> dict:
    tok = _fetch_token()
    if not tok:
        raise RuntimeError("No anti-forgery token present in cookies")
    return {**hdr, "X-XSRF-TOKEN": tok}

def get_total_count(payload: dict) -> int:
    """Get the total count of appointments using the JSON endpoint."""
    print("📊  Getting total count of appointments...")

    # Use a small page size just to get the total count
    count_payload = payload.copy()
    count_payload["skip"] = 0
    count_payload["pageSize"] = 1  # Just need the count

    r = s.post(
        f"{BASE}/core/api/scheduling/scheduling/GetEventsListNewAsync",
        json=count_payload,
        headers=_with_token(HEADERS),
        timeout=60,
    )

    if r.status_code != 200:
        print(f"❌  Count request failed — {r.status_code}")
        print(r.text[:1000])
        r.raise_for_status()

    data = r.json()
    total = data.get("totalCount", 0)
    print(f"📈  Total appointments found: {total}")
    return total

def download_excel_directly(payload: dict, outfile: str) -> None:
    """
    Download Excel file directly from GetAppointmentsListPrintAsync endpoint.
    This endpoint returns the Excel file directly, no need for JSON processing.
    """
    print("📥  Downloading Excel file directly from API...")

    # First, get the total count to understand how much data we're dealing with
    total_count = get_total_count(payload)

    # Try multiple approaches to get all data
    approaches = [
        {"name": "No pagination", "skip": None, "pageSize": None},
        {"name": "Large pageSize", "skip": 0, "pageSize": max(1000, total_count + 100)},
        {"name": "Browser-like", "skip": 0, "pageSize": 100},
        {"name": "Exact browser payload", "skip": 100, "pageSize": 100, "clear_filters": True},
    ]

    largest_size = 0
    largest_file = None

    for i, approach in enumerate(approaches):
        print(f"\n📋  Attempt {i+1}: {approach['name']}")

        # Create a proper payload for the download endpoint
        payload_clean = payload.copy()

        # Set pagination parameters based on approach
        if approach["skip"] is not None:
            payload_clean["skip"] = approach["skip"]
        else:
            payload_clean.pop("skip", None)

        if approach["pageSize"] is not None:
            payload_clean["pageSize"] = approach["pageSize"]
        else:
            payload_clean.pop("pageSize", None)

        # Add the missing field from the example request
        payload_clean["sameLocationStaffIds"] = []

        # Handle special case: exact browser payload (clear filters like browser does)
        if approach.get("clear_filters"):
            print("�  Using exact browser payload structure (clearing filters)")
            payload_clean["memberIds"] = []
            payload_clean["staffIds"] = []
            if "filterOptions" in payload_clean:
                payload_clean["filterOptions"]["memberIds"] = []
                payload_clean["filterOptions"]["staffIds"] = []
            # Also change date format to match browser
            payload_clean["startDate"] = "6/12/2025, 12:00:00 AM"
            payload_clean["endDate"] = "6/19/2025, 12:00:00 AM"
            if "filterOptions" in payload_clean:
                payload_clean["filterOptions"]["startDate"] = "6/12/2025, 12:00:00 AM"
                payload_clean["filterOptions"]["endDate"] = "6/19/2025, 12:00:00 AM"
        else:
            # Ensure we have the member and staff IDs (don't clear them!)
            if "filterOptions" in payload_clean:
                payload_clean["filterOptions"]["memberIds"] = payload_clean.get("memberIds", [])
                payload_clean["filterOptions"]["staffIds"] = payload_clean.get("staffIds", [])

        print(f"📋  Filtering by {len(payload_clean.get('memberIds', []))} members and {len(payload_clean.get('staffIds', []))} staff")
        if "pageSize" in payload_clean:
            print(f"📄  Using skip: {payload_clean.get('skip', 'None')}, pageSize: {payload_clean['pageSize']}")

        r = s.post(
            f"{BASE}/core/api/scheduling/scheduling/GetAppointmentsListPrintAsync",
            json=payload_clean,
            headers=_with_token(HEADERS),
            timeout=120,  # Longer timeout for potentially large files
        )

        if r.status_code != 200:
            print(f"❌  Excel download failed — {r.status_code}")
            print(r.text[:1000])
            continue  # Try next approach

        # Check content type to determine how to handle the response
        content_type = r.headers.get('content-type', '').lower()
        print(f"📋  Response content-type: {content_type}")

        # Generate filename for this attempt
        attempt_file = outfile.replace('.xlsx', f'_attempt_{i+1}.xlsx')

        if 'spreadsheet' in content_type or 'excel' in content_type:
            # Direct binary Excel file
            with open(attempt_file, 'wb') as f:
                f.write(r.content)
            print(f"✅  Downloaded Excel file directly → {attempt_file}")
        else:
            # Might be base64 encoded or JSON response
            try:
                # Try to decode as base64
                excel_data = base64.b64decode(r.text)
                with open(attempt_file, 'wb') as f:
                    f.write(excel_data)
                print(f"✅  Downloaded Excel file (base64 decoded) → {attempt_file}")
            except Exception as e:
                # Save response for debugging
                debug_file = attempt_file.replace('.xlsx', '_debug.txt')
                with open(debug_file, 'w') as f:
                    f.write(f"Content-Type: {content_type}\n")
                    f.write(f"Status: {r.status_code}\n")
                    f.write(f"Headers: {dict(r.headers)}\n\n")
                    f.write(r.text)
                print(f"❌  Unexpected response format. Debug info saved → {debug_file}")
                print(f"Error: {e}")
                continue

        # Show file size
        file_size = os.path.getsize(attempt_file)
        print(f"📊  File size: {file_size:,} bytes ({file_size/1024:.1f} KB)")

        # If this is the largest file so far, copy it to the main output file
        if i == 0 or file_size > largest_size:
            largest_size = file_size
            largest_file = attempt_file

    # Copy the largest file to the main output filename
    if 'largest_file' in locals():
        import shutil
        shutil.copy2(largest_file, outfile)
        print(f"\n🏆  Best result: {largest_file} ({largest_size:,} bytes) → {outfile}")
    else:
        print("❌  All download attempts failed")

# ── STEP 0-2: authenticate (unchanged) ────────────────────────────────────────
s.get(f"{BASE}/HealthCare", headers=HEADERS).raise_for_status()
s.post(f"{BASE}/HealthCare/SingleSignOn/GetAuthenticationDetail",
       json={"User": EMAIL}, headers=_with_token(HEADERS)).raise_for_status()
print("✅  Email verified")
s.post(f"{BASE}/HealthCare/User/Login",
       json={"User": EMAIL, "Password": PASSWORD, "setPermissions": True},
       headers=_with_token(HEADERS)).raise_for_status()
print("✅  Logged in")
s.get(f"{BASE}/core/scheduler/appointments",
      headers=_with_token(HEADERS)).raise_for_status()
print("✅  Anti-forgery token ready")

# ── STEP 3: build payload (dates linked!) ─────────────────────────────────────
# ── Time range and shared lists ──────────────────────────────────────────────
# Use the exact format from the website's download request
START = "6/12/2025, 3:00:00 AM"  # Keep the 3:00 AM format for consistency
END   = "6/19/2025, 3:00:00 AM"

MEMBER_IDS = [
    538160, 538960, 558267, 563649, 563708, 583650, 584496, 587576,
    599247, 623283, 628487, 631789, 642691, 647215, 649209, 659401,
    677789, 715312, 715313, 732653, 742072, 745710, 785137, 797141,
    797147, 800951, 818463, 818464, 818465, 840574, 851785, 862597,
    862598, 875206, 898539, 898540, 901333, 909360, 915659, 924605,
    931706
]

STAFF_IDS = [
    207014, 207378, 217423, 220417, 220443, 231732, 232218, 234101,
    240153, 252207, 254937, 256827, 262435, 264792, 265890, 271750,
    281251, 301757, 301758, 311385, 316287, 318261, 341594, 347881,
    347886, 349815, 359039, 359040, 359041, 368701, 375034, 381113,
    381114, 388710, 402051, 402052, 403689, 408177, 411746, 416840,
    421072
]

# ── Final payload structure (matching UI request) ─────────────────────────────
payload_base = {
    "startDate": START,
    "endDate": END,
    "checkExceedAuthorization": True,
    "showEVV": 0,
    "memberIds": MEMBER_IDS,
    "clientIds": [],
    "staffIds": STAFF_IDS,
    "timeFormat": "hh:mm tt",
    "dateFormat": "MM/dd/yyyy",
    "includeAssignedOnly": False,
    "sorting": {
        "field": "date",
        "asc": False,
        "type": 1
    },
    "filterOptions": {
        "startDate": START,
        "endDate": END,
        "appointmentStatus": [
            "Scheduled", "Needs Verification", "Completed",
            "Cancelled - Needs Reschedule", "Cancelled - Rescheduled",
            "Cancelled - Cannot Reschedule"
        ],
        "billedStatus": [],
        "sessionNoteStatus": [],
        "sessionNoteAlerts": [],
        "appointmentLocation": [],
        "location": [],
        "appointmentType": [],
        "payableTypes": [],
        "authRequirements": [],
        "CMFDate": None,
        "CMTDate": None,
        "appointmentRequirements": ["staffVerification"],
        "authorizationType": [],
        "appointmentId": None,
        "authorizationNumber": "",
        "billingCodes": None,
        "acknowledgeableExceptions": [],
        "EVVReasonCodes": [],
        "EVVStatus": [],
        "missingClockedIn": None,
        "missingClockedOut": None,
        "renderingProviders": [],
        "service": [],
        "serviceLineId": 6476,
        "showEVV": False,
        "memberIds": MEMBER_IDS,  # Add memberIds to filterOptions
        "clientIds": [],
        "staffIds": STAFF_IDS,
        "validations": [],
        "clientsOptions": [],
    },
    "schedulerPermissionLevelTypeId": 2,
}


# ── Download Excel directly ──────────────────────────────────────────────────
print("🚀  Starting direct Excel download...")
download_excel_directly(payload_base, "appointments_june_2025_direct.xlsx")

# ── Also test JSON download for comparison ───────────────────────────────────
print("\n🔍  Testing JSON download for comparison...")

def fetch_all_json_data(payload: dict) -> list:
    """Fetch all JSON data with pagination to compare with Excel."""
    all_events = []
    skip = 0
    page_size = 100

    while True:
        test_payload = payload.copy()
        test_payload["skip"] = skip
        test_payload["pageSize"] = page_size

        r = s.post(
            f"{BASE}/core/api/scheduling/scheduling/GetEventsListNewAsync",
            json=test_payload,
            headers=_with_token(HEADERS),
            timeout=60,
        )

        if r.status_code != 200:
            print(f"❌  JSON fetch failed — {r.status_code}")
            break

        data = r.json()
        events = data.get("events", [])
        all_events.extend(events)

        total = data.get("totalCount", len(all_events))
        print(f"📄  JSON Page {skip//page_size + 1}: {len(events)} events (total so far: {len(all_events)}/{total})")

        if len(all_events) >= total or len(events) < page_size:
            break

        skip += page_size

    return all_events

json_events = fetch_all_json_data(payload_base)
print(f"📊  JSON total events fetched: {len(json_events)}")

print("🎉  All downloads completed!")
