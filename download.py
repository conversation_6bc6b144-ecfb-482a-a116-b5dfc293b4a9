#!/usr/bin/env python3
"""
Download full appointment list from Rethink → Excel
"""

import os, requests, struct
from dotenv import load_dotenv
from openpyxl import load_workbook          # pip install openpyxl

# ── Credentials ──────────────────────────────────────────────────────────────
load_dotenv()
EMAIL    = os.getenv("RTHINK_USER")
PASSWORD = os.getenv("RTHINK_PASS")
if not EMAIL or not PASSWORD:
    raise EnvironmentError("Add RTHINK_USER and RTHINK_PASS to .env")

# ── Constants ────────────────────────────────────────────────────────────────
BASE   = "https://webapp.rethinkbehavioralhealth.com"
START  = "6/12/2025, 3:00:00 AM"
END    = "6/19/2025, 3:00:00 AM"
OUTFILE = "AppointmentList.xlsx"

HEADERS = {
    "Content-Type": "application/json;charset=utf-8",
    "Accept": "application/json, text/plain, */*",
    "X-Application-Key": "74569e11-18b4-4122-a58d-a4b830aa12c4",
    "X-Origin": "Angular",
    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64; rv:139.0) Gecko/139.0",
    "Origin": BASE,
    "Referer": f"{BASE}/Healthcare#/Login",
}


MEMBER_IDS = [
    538160, 538960, 558267, 563649, 563708, 583650, 584496, 587576,
    599247, 623283, 628487, 631789, 642691, 647215, 649209, 659401,
    677789, 715312, 715313, 732653, 742072, 745710, 785137, 797141,
    797147, 800951, 818463, 818464, 818465, 840574, 851785, 862597,
    862598, 875206, 898539, 898540, 901333, 909360, 915659, 924605,
    931706
]

STAFF_IDS = [
    207014, 207378, 217423, 220417, 220443, 231732, 232218, 234101,
    240153, 252207, 254937, 256827, 262435, 264792, 265890, 271750,
    281251, 301757, 301758, 311385, 316287, 318261, 341594, 347881,
    347886, 349815, 359039, 359040, 359041, 368701, 375034, 381113,
    381114, 388710, 402051, 402052, 403689, 408177, 411746, 416840,
    421072
]

# ── Session helpers ──────────────────────────────────────────────────────────
s = requests.Session()

def _token():
    for c in s.cookies:
        if "XSRF" in c.name.upper() or "ANTIFORGERY" in c.name.upper():
            return c.value
    raise RuntimeError("XSRF token not found")

def t(h): return {**h, "X-XSRF-TOKEN": _token()}

# ── 0–2: log in ──────────────────────────────────────────────────────────────
s.get(f"{BASE}/HealthCare", headers=HEADERS).raise_for_status()
s.post(f"{BASE}/HealthCare/SingleSignOn/GetAuthenticationDetail",
       json={"User": EMAIL}, headers=t(HEADERS)).raise_for_status()
s.post(f"{BASE}/HealthCare/User/Login",
       json={"User": EMAIL, "Password": PASSWORD, "setPermissions": True},
       headers=t(HEADERS)).raise_for_status()
s.get(f"{BASE}/core/scheduler/appointments", headers=t(HEADERS)).raise_for_status()
print("✅  Authenticated")

# ── Optional sanity check: total count via JSON endpoint ─────────────────────
count_payload = {
    "startDate": START, "endDate": END,
    "checkExceedAuthorization": True, "showEVV": 0,
    "memberIds": MEMBER_IDS, "clientIds": [], "staffIds": STAFF_IDS,
    "timeFormat": "hh:mm tt", "dateFormat": "MM/dd/yyyy",
    "skip": 0, "pageSize": 1, "includeAssignedOnly": False,
    "sorting": {"field": "date", "asc": False, "type": 1},
    "filterOptions": {
        "startDate": START, "endDate": END,
        "appointmentStatus": [
            "Scheduled", "Needs Verification", "Completed",
            "Cancelled - Needs Reschedule", "Cancelled - Rescheduled",
            "Cancelled - Cannot Reschedule"],
        "serviceLineId": 6476, "appointmentRequirements": ["staffVerification"],
        "showEVV": False, "clientIds": [], "staffIds": STAFF_IDS,
        "billedStatus":[],"sessionNoteStatus":[],"sessionNoteAlerts":[],
    },
    "schedulerPermissionLevelTypeId": 2,
}
total = s.post(f"{BASE}/core/api/scheduling/scheduling/GetEventsListNewAsync",
               json=count_payload, headers=t(HEADERS)).json()["totalCount"]
print(f"📊  Expected rows: {total}")

# ── Excel export (no skip/pageSize) ──────────────────────────────────────────
export_payload = {k: count_payload[k] for k in (
    "startDate","endDate","checkExceedAuthorization","showEVV","memberIds",
    "clientIds","staffIds","timeFormat","dateFormat","includeAssignedOnly",
    "sorting","filterOptions","schedulerPermissionLevelTypeId")}
export_payload["sameLocationStaffIds"] = []

resp = s.post(
    f"{BASE}/core/api/scheduling/scheduling/GetAppointmentsListPrintAsync",
    json=export_payload, headers=t(HEADERS), timeout=90)
resp.raise_for_status()

with open(OUTFILE, "wb") as f:
    f.write(resp.content)
print(f"✅  Saved → {OUTFILE}")

# ── Row-count sanity — quick read with openpyxl (no pandas needed) ────────────
wb = load_workbook(OUTFILE, read_only=True)
rows = wb.active.max_row - 1        # minus header row
print(f"📄  Rows in sheet: {rows}")
if rows != total:
    print("⚠️  Mismatch! JSON said", total)
else:
    print("🎉  Row count matches JSON total")
