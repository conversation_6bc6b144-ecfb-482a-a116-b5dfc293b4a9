#!/usr/bin/env python3
"""
Download Rethink BH appointments → Excel (optimized version with async support)
"""

import asyncio
import json
import logging
import os
import time
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Any, AsyncGenerator, Dict, List, Optional, Union

import aiohttp
import pandas as pd
from dotenv import load_dotenv

# ── Configuration ─────────────────────────────────────────────────────────────
@dataclass
class Config:
    """Configuration settings for the Rethink API client."""
    email: str
    password: str
    base_url: str = "https://webapp.rethinkbehavioralhealth.com"
    page_size: int = 100
    max_concurrent_requests: int = 5
    request_timeout: int = 60
    max_retries: int = 3
    retry_delay: float = 1.0
    headers: Dict[str, str] = field(default_factory=lambda: {
        "Content-Type": "application/json;charset=utf-8",
        "Accept": "application/json, text/plain, */*",
        "X-Application-Key": "74569e11-18b4-4122-a58d-a4b830aa12c4",
        "X-Origin": "Angular",
        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64; rv:139.0) Gecko/139.0",
    })

    def __post_init__(self):
        self.headers.update({
            "Origin": self.base_url,
            "Referer": f"{self.base_url}/Healthcare#/Login",
        })

def load_config() -> Config:
    """Load configuration from environment variables."""
    load_dotenv()
    email = os.getenv("RTHINK_USER")
    password = os.getenv("RTHINK_PASS")

    if not email or not password:
        raise EnvironmentError("Add RTHINK_USER and RTHINK_PASS to .env")

    return Config(email=email, password=password)

# ── Logging Setup ────────────────────────────────────────────────────────────
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('rethink_download.log')
    ]
)
logger = logging.getLogger(__name__)

# ── Async API Client ─────────────────────────────────────────────────────────
class RethinkAPIClient:
    """Async API client for Rethink Behavioral Health."""

    def __init__(self, config: Config):
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self.auth_token: Optional[str] = None
        self.semaphore = asyncio.Semaphore(config.max_concurrent_requests)

    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[aiohttp.ClientSession, None]:
        """Context manager for aiohttp session."""
        if self.session is None:
            connector = aiohttp.TCPConnector(
                limit=self.config.max_concurrent_requests * 2,
                limit_per_host=self.config.max_concurrent_requests,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            timeout = aiohttp.ClientTimeout(total=self.config.request_timeout)
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers=self.config.headers
            )

        try:
            yield self.session
        finally:
            pass  # Keep session alive for reuse

    async def close(self):
        """Close the aiohttp session."""
        if self.session:
            await self.session.close()
            self.session = None

    def _extract_token_from_cookies(self, cookies: aiohttp.CookieJar) -> Optional[str]:
        """Extract anti-forgery token from cookies."""
        for cookie in cookies:
            if any(k in cookie.key.upper() for k in ("XSRF", "ANTIFORGERY", "REQUESTVERIFICATIONTOKEN")):
                return cookie.value
        return None

    def _add_token_to_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """Add anti-forgery token to headers."""
        if not self.auth_token:
            raise RuntimeError("No anti-forgery token available")
        return {**headers, "X-XSRF-TOKEN": self.auth_token}

    async def _make_request_with_retry(
        self,
        method: str,
        url: str,
        **kwargs
    ) -> aiohttp.ClientResponse:
        """Make HTTP request with retry logic."""
        async with self.semaphore:
            for attempt in range(self.config.max_retries + 1):
                try:
                    async with self.get_session() as session:
                        async with session.request(method, url, **kwargs) as response:
                            if response.status == 200:
                                return response
                            elif attempt == self.config.max_retries:
                                response.raise_for_status()
                            else:
                                logger.warning(f"Request failed (attempt {attempt + 1}): {response.status}")
                                await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
                except asyncio.TimeoutError:
                    if attempt == self.config.max_retries:
                        raise
                    logger.warning(f"Request timeout (attempt {attempt + 1})")
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
                except Exception as e:
                    if attempt == self.config.max_retries:
                        raise
                    logger.warning(f"Request error (attempt {attempt + 1}): {e}")
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))

        raise RuntimeError("Max retries exceeded")

    async def authenticate(self) -> None:
        """Authenticate with the Rethink API."""
        logger.info("Starting authentication process...")

        # Step 1: Initial request to get cookies
        async with self.get_session() as session:
            async with session.get(f"{self.config.base_url}/HealthCare") as response:
                response.raise_for_status()
                self.auth_token = self._extract_token_from_cookies(session.cookie_jar)

        if not self.auth_token:
            raise RuntimeError("Failed to obtain initial auth token")

        # Step 2: Verify email
        headers = self._add_token_to_headers(self.config.headers)
        async with self.get_session() as session:
            async with session.post(
                f"{self.config.base_url}/HealthCare/SingleSignOn/GetAuthenticationDetail",
                json={"User": self.config.email},
                headers=headers
            ) as response:
                response.raise_for_status()
                self.auth_token = self._extract_token_from_cookies(session.cookie_jar) or self.auth_token

        logger.info("✅ Email verified")

        # Step 3: Login
        headers = self._add_token_to_headers(self.config.headers)
        async with self.get_session() as session:
            async with session.post(
                f"{self.config.base_url}/HealthCare/User/Login",
                json={
                    "User": self.config.email,
                    "Password": self.config.password,
                    "setPermissions": True
                },
                headers=headers
            ) as response:
                response.raise_for_status()
                self.auth_token = self._extract_token_from_cookies(session.cookie_jar) or self.auth_token

        logger.info("✅ Logged in")

        # Step 4: Get scheduler access
        headers = self._add_token_to_headers(self.config.headers)
        async with self.get_session() as session:
            async with session.get(
                f"{self.config.base_url}/core/scheduler/appointments",
                headers=headers
            ) as response:
                response.raise_for_status()
                self.auth_token = self._extract_token_from_cookies(session.cookie_jar) or self.auth_token

        logger.info("✅ Authentication complete")

    async def fetch_events_page(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Fetch a single page of events."""
        headers = self._add_token_to_headers(self.config.headers)

        async with self.get_session() as session:
            async with session.post(
                f"{self.config.base_url}/core/api/scheduling/scheduling/GetEventsListNewAsync",
                json=payload,
                headers=headers
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"Page fetch failed: {response.status} - {error_text[:1000]}")
                    response.raise_for_status()

                return await response.json()

    async def fetch_all_events(self, payload_base: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch all events with pagination."""
        all_events = []
        skip = 0
        page_num = 1

        while True:
            payload = {**payload_base, "skip": skip, "pageSize": self.config.page_size}

            try:
                chunk = await self.fetch_events_page(payload)
                events = chunk.get("events", [])
                all_events.extend(events)

                total = chunk.get("totalCount", len(all_events))
                logger.info(f"📄 Page {page_num}: {len(events)} events (total: {len(all_events)}/{total})")

                if len(all_events) >= total or len(events) < self.config.page_size:
                    break

                skip += self.config.page_size
                page_num += 1

            except Exception as e:
                logger.error(f"Failed to fetch page {page_num}: {e}")
                raise

        logger.info(f"✅ Fetched {len(all_events)} total events")
        return all_events

def export_events_to_excel(events: List[Dict[str, Any]], outfile: str) -> None:
    """Export events to Excel with optimized memory usage."""
    if not events:
        logger.warning("⚠️  No appointment events found.")
        return

    def process_events_generator():
        """Generator to process events one by one for memory efficiency."""
        for e in events:
            appt = e.get("appt", {})
            base_data = {
                "AppointmentID": appt.get("id"),
                "ClientName": appt.get("clientName"),
                "StaffName": appt.get("staffName"),
                "Status": appt.get("statusName"),
                "Location": appt.get("locationName"),
                "Service": appt.get("serviceName"),
                "Funder": appt.get("funderName"),
                "BillingCode": appt.get("billingCode"),
            }

            for ev in appt.get("events", []):
                row = {
                    **base_data,
                    "EventID": ev.get("id"),
                    "Start": _format_datetime(ev.get("start")),
                    "End": _format_datetime(ev.get("end")),
                    "Minutes": ev.get("minutes"),
                    "Hours": ev.get("hours"),
                    "EventStatus": ev.get("statusName"),
                    "EVVStatus": ev.get("evvStatusName"),
                }
                yield row

    # Process in chunks for better memory management
    chunk_size = 1000
    rows = []
    total_rows = 0

    for row in process_events_generator():
        rows.append(row)
        total_rows += 1

        if len(rows) >= chunk_size:
            # For large datasets, we could write to Excel in chunks
            # For now, we'll collect all rows since pandas.to_excel doesn't support append mode
            pass

    # Create DataFrame and export
    df = pd.DataFrame(rows)
    df.to_excel(outfile, index=False, engine='openpyxl')
    logger.info(f"✅ Exported {total_rows} rows → {outfile}")

def _format_datetime(dt_str: Optional[str]) -> Optional[str]:
    """Format datetime string safely."""
    if not dt_str:
        return dt_str
    try:
        dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
        return dt.strftime("%Y-%m-%d %I:%M %p")
    except (ValueError, AttributeError):
        return dt_str

# ── Data Configuration ──────────────────────────────────────────────────────
@dataclass
class DataConfig:
    """Configuration for the data to fetch."""
    start_date: str = "6/12/2025, 3:00:00 AM"
    end_date: str = "6/19/2025, 3:00:00 AM"
    member_ids: List[int] = field(default_factory=lambda: [
        538160, 538960, 558267, 563649, 563708, 583650, 584496, 587576,
        599247, 623283, 628487, 631789, 642691, 647215, 649209, 659401,
        677789, 715312, 715313, 732653, 742072, 745710, 785137, 797141,
        797147, 800951, 818463, 818464, 818465, 840574, 851785, 862597,
        862598, 875206, 898539, 898540, 901333, 909360, 915659, 924605,
        931706
    ])
    staff_ids: List[int] = field(default_factory=lambda: [
        207014, 207378, 217423, 220417, 220443, 231732, 232218, 234101,
        240153, 252207, 254937, 256827, 262435, 264792, 265890, 271750,
        281251, 301757, 301758, 311385, 316287, 318261, 341594, 347881,
        347886, 349815, 359039, 359040, 359041, 368701, 375034, 381113,
        381114, 388710, 402051, 402052, 403689, 408177, 411746, 416840,
        421072
    ])

    def get_payload_base(self) -> Dict[str, Any]:
        """Generate the base payload for API requests."""
        return {
            "startDate": self.start_date,
            "endDate": self.end_date,
            "checkExceedAuthorization": True,
            "showEVV": 0,
            "memberIds": self.member_ids,
            "clientIds": [],
            "staffIds": self.staff_ids,
            "timeFormat": "hh:mm tt",
            "dateFormat": "MM/dd/yyyy",
            "includeAssignedOnly": False,
            "sorting": {
                "field": "date",
                "asc": False,
                "type": 1
            },
            "filterOptions": {
                "startDate": self.start_date,
                "endDate": self.end_date,
                "appointmentStatus": [
                    "Scheduled", "Needs Verification", "Completed",
                    "Cancelled - Needs Reschedule", "Cancelled - Rescheduled",
                    "Cancelled - Cannot Reschedule"
                ],
                "billedStatus": [],
                "sessionNoteStatus": [],
                "sessionNoteAlerts": [],
                "appointmentLocation": [],
                "location": [],
                "appointmentType": [],
                "payableTypes": [],
                "authRequirements": [],
                "CMFDate": None,
                "CMTDate": None,
                "appointmentRequirements": ["staffVerification"],
                "authorizationType": [],
                "appointmentId": None,
                "authorizationNumber": "",
                "billingCodes": None,
                "acknowledgeableExceptions": [],
                "EVVReasonCodes": [],
                "EVVStatus": [],
                "missingClockedIn": None,
                "missingClockedOut": None,
                "renderingProviders": [],
                "service": [],
                "serviceLineId": 6476,
                "showEVV": False,
                "clientIds": [],
                "staffIds": self.staff_ids,
                "validations": [],
                "clientsOptions": [],
            },
            "schedulerPermissionLevelTypeId": 2,
        }

# ── Main Execution ───────────────────────────────────────────────────────────
async def main():
    """Main execution function."""
    start_time = time.time()

    try:
        # Load configuration
        config = load_config()
        data_config = DataConfig()

        logger.info("🚀 Starting Rethink appointment download...")

        # Initialize API client
        client = RethinkAPIClient(config)

        try:
            # Authenticate
            await client.authenticate()

            # Fetch events
            payload_base = data_config.get_payload_base()
            events = await client.fetch_all_events(payload_base)

            # Save raw data
            raw_file = "appointments_raw_all.json"
            with open(raw_file, "w", encoding="utf-8") as f:
                json.dump({"events": events}, f, indent=2, ensure_ascii=False)
            logger.info(f"💾 Saved raw response ({len(events)} events) → {raw_file}")

            # Export to Excel
            excel_file = "appointments_june_2025_all.xlsx"
            export_events_to_excel(events, excel_file)

            elapsed = time.time() - start_time
            logger.info(f"🎉 Download completed in {elapsed:.2f} seconds")

        finally:
            await client.close()

    except Exception as e:
        logger.error(f"❌ Download failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
