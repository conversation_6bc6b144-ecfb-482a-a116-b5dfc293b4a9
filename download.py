#!/usr/bin/env python3
"""
Download Rethink BH appointments → Excel (auto-pagination, robust payload)
"""

import os, json, requests, pandas as pd
from dotenv import load_dotenv
from datetime import datetime

# ── Config ────────────────────────────────────────────────────────────────────
load_dotenv()
EMAIL    = os.getenv("RTHINK_USER")
PASSWORD = os.getenv("RTHINK_PASS")
if not EMAIL or not PASSWORD:
    raise EnvironmentError("Add RTHINK_USER and RTHINK_PASS to .env")

BASE = "https://webapp.rethinkbehavioralhealth.com"
HEADERS = {
    "Content-Type": "application/json;charset=utf-8",
    "Accept": "application/json, text/plain, */*",
    "X-Application-Key": "74569e11-18b4-4122-a58d-a4b830aa12c4",
    "X-Origin": "Ang<PERSON>",
    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64; rv:139.0) Gecko/139.0",
    "Origin": BASE,
    "Referer": f"{BASE}/Healthcare#/Login",
}

s = requests.Session()

# ── Helpers ──────────────────────────────────────────────────────────────────
def _fetch_token() -> str | None:
    for c in s.cookies:
        if any(k in c.name.upper() for k in ("XSRF", "ANTIFORGERY", "REQUESTVERIFICATIONTOKEN")):
            return c.value
    return None

def _with_token(hdr: dict) -> dict:
    tok = _fetch_token()
    if not tok:
        raise RuntimeError("No anti-forgery token present in cookies")
    return {**hdr, "X-XSRF-TOKEN": tok}

def fetch_events(payload_base: dict, page_size: int = 100) -> list[dict]:
    events, skip = [], 0
    while True:
        payload = {**payload_base, "skip": skip, "pageSize": page_size}
        r = s.post(
            f"{BASE}/core/api/scheduling/scheduling/GetEventsListNewAsync",
            json=payload,
            headers=_with_token(HEADERS),
            timeout=60,
        )
        if r.status_code != 200:
            print(f"❌  Page {skip//page_size+1} failed — {r.status_code}")
            print(r.text[:1000])          # show first 1 000 chars of the error
            r.raise_for_status()
        chunk = r.json()

        events.extend(chunk.get("events", []))
        total = chunk.get("totalCount", len(events))
        if len(events) >= total or len(chunk.get("events", [])) < page_size:
            break
        skip += page_size
    return events

def export_events_to_excel(events: list[dict], outfile: str) -> None:
    rows = []
    for e in events:
        appt = e.get("appt", {})
        base = {                 # info that repeats on every event row
            "AppointmentID": appt.get("id"),
            "ClientName":    appt.get("clientName"),
            "StaffName":     appt.get("staffName"),
            "Status":        appt.get("statusName"),
            "Location":      appt.get("locationName"),
            "Service":       appt.get("serviceName"),
            "Funder":        appt.get("funderName"),
            "BillingCode":   appt.get("billingCode"),
        }
        for ev in appt.get("events", []):
            row = base | {      # 3.10+ dict merge
                "EventID":     ev.get("id"),
                "Start":       ev.get("start"),
                "End":         ev.get("end"),
                "Minutes":     ev.get("minutes"),
                "Hours":       ev.get("hours"),
                "EventStatus": ev.get("statusName"),
                "EVVStatus":   ev.get("evvStatusName"),
            }
            for k in ("Start", "End"):
                try:
                    row[k] = datetime.fromisoformat(row[k]).strftime("%Y-%m-%d %I:%M %p")
                except Exception:
                    pass
            rows.append(row)

    if not rows:
        print("⚠️  No appointment events found.")
        return
    pd.DataFrame(rows).to_excel(outfile, index=False)
    print(f"✅  Exported {len(rows)} rows → {outfile}")

# ── STEP 0-2: authenticate (unchanged) ────────────────────────────────────────
s.get(f"{BASE}/HealthCare", headers=HEADERS).raise_for_status()
s.post(f"{BASE}/HealthCare/SingleSignOn/GetAuthenticationDetail",
       json={"User": EMAIL}, headers=_with_token(HEADERS)).raise_for_status()
print("✅  Email verified")
s.post(f"{BASE}/HealthCare/User/Login",
       json={"User": EMAIL, "Password": PASSWORD, "setPermissions": True},
       headers=_with_token(HEADERS)).raise_for_status()
print("✅  Logged in")
s.get(f"{BASE}/core/scheduler/appointments",
      headers=_with_token(HEADERS)).raise_for_status()
print("✅  Anti-forgery token ready")

# ── STEP 3: build payload (dates linked!) ─────────────────────────────────────
# ── Time range and shared lists ──────────────────────────────────────────────
START = "6/12/2025, 3:00:00 AM"
END   = "6/19/2025, 3:00:00 AM"

MEMBER_IDS = [
    538160, 538960, 558267, 563649, 563708, 583650, 584496, 587576,
    599247, 623283, 628487, 631789, 642691, 647215, 649209, 659401,
    677789, 715312, 715313, 732653, 742072, 745710, 785137, 797141,
    797147, 800951, 818463, 818464, 818465, 840574, 851785, 862597,
    862598, 875206, 898539, 898540, 901333, 909360, 915659, 924605,
    931706
]

STAFF_IDS = [
    207014, 207378, 217423, 220417, 220443, 231732, 232218, 234101,
    240153, 252207, 254937, 256827, 262435, 264792, 265890, 271750,
    281251, 301757, 301758, 311385, 316287, 318261, 341594, 347881,
    347886, 349815, 359039, 359040, 359041, 368701, 375034, 381113,
    381114, 388710, 402051, 402052, 403689, 408177, 411746, 416840,
    421072
]

# ── Final payload structure (matching UI request) ─────────────────────────────
payload_base = {
    "startDate": START,
    "endDate": END,
    "checkExceedAuthorization": True,
    "showEVV": 0,
    "memberIds": MEMBER_IDS,
    "clientIds": [],
    "staffIds": STAFF_IDS,
    "timeFormat": "hh:mm tt",
    "dateFormat": "MM/dd/yyyy",
    "includeAssignedOnly": False,
    "sorting": {
        "field": "date",
        "asc": False,
        "type": 1
    },
    "filterOptions": {
        "startDate": START,
        "endDate": END,
        "appointmentStatus": [
            "Scheduled", "Needs Verification", "Completed",
            "Cancelled - Needs Reschedule", "Cancelled - Rescheduled",
            "Cancelled - Cannot Reschedule"
        ],
        "billedStatus": [],
        "sessionNoteStatus": [],
        "sessionNoteAlerts": [],
        "appointmentLocation": [],
        "location": [],
        "appointmentType": [],
        "payableTypes": [],
        "authRequirements": [],
        "CMFDate": None,
        "CMTDate": None,
        "appointmentRequirements": ["staffVerification"],
        "authorizationType": [],
        "appointmentId": None,
        "authorizationNumber": "",
        "billingCodes": None,
        "acknowledgeableExceptions": [],
        "EVVReasonCodes": [],
        "EVVStatus": [],
        "missingClockedIn": None,
        "missingClockedOut": None,
        "renderingProviders": [],
        "service": [],
        "serviceLineId": 6476,
        "showEVV": False,
        "clientIds": [],
        "staffIds": STAFF_IDS,
        "validations": [],
        "clientsOptions": [],
    },
    "schedulerPermissionLevelTypeId": 2,
}


# ── Fetch, save, export ──────────────────────────────────────────────────────
events = fetch_events(payload_base)
with open("appointments_raw_all.json", "w", encoding="utf-8") as f:
    json.dump({"events": events}, f, indent=2, ensure_ascii=False)
print(f"💾  Saved raw response ({len(events)} events)")
export_events_to_excel(events, "appointments_june_2025_all.xlsx")
