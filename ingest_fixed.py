import os
import psycopg2
from urllib.parse import urlparse
from dotenv import load_dotenv
import pandas as pd

# Load environment variables
load_dotenv()
db_url = os.getenv("SUPABASE_DB_URL")

if not db_url:
    raise EnvironmentError("SUPABASE_DB_URL not found in environment variables")

print("🔗 Connecting to Supabase database...")

# Parse connection URI
parsed = urlparse(db_url)
conn = psycopg2.connect(
    dbname=parsed.path[1:],  # Remove leading slash
    user=parsed.username,
    password=parsed.password,
    host=parsed.hostname,
    port=parsed.port
)

print("✅ Connected to database")

# Find the most recent Excel file
excel_files = [
    "appointments_june_2025_browser_compliant.xlsx",
    "appointments_june_2025_direct.xlsx", 
    "appointments_june_2025_final.xlsx",
    "appointments_December_2024_to_June_2025.xlsx"
]

excel_file = None
for filename in excel_files:
    if os.path.exists(filename):
        excel_file = filename
        break

if not excel_file:
    raise FileNotFoundError(f"No Excel file found. Looking for: {excel_files}")

print(f"📊 Loading Excel file: {excel_file}")

# Load Excel file - skip the empty first row
print("📊 Loading Excel with skiprows=1 to skip empty first row...")
df = pd.read_excel(excel_file, skiprows=1)
print(f"📈 Loaded {len(df)} rows from Excel")
print(f"📋 Columns found: {df.columns.tolist()}")

# Show first row for inspection
if len(df) > 0:
    print(f"📄 First row sample:")
    for col in df.columns:
        print(f"   {col}: {df.iloc[0][col]}")

# Define column mapping from Excel to database
COLUMN_MAPPING = {
    # Common Excel column names to database columns
    'Appointment Type': 'appointmentType',
    'Appointment Tag': 'appointmentTag', 
    'Service Line': 'serviceLine',
    'Service': 'service',
    'Appointment Location': 'appointmentLocation',
    'Location': 'appointmentLocation',  # Alternative name
    'Duration': 'duration',
    'Day': 'day',
    'Date': 'date',
    'Time': 'time',
    'Scheduled Date': 'scheduledDate',
    'Modified Date': 'modifiedDate',
    'Client': 'client',
    'Client Name': 'client',  # Alternative name
    'Staff': 'staff',
    'Staff Name': 'staff',  # Alternative name
    'Staff Member': 'staff',  # Alternative name
    'Status': 'status',
    'Session Note': 'sessionNote',
    'Staff Verification': 'staffVerification',
    'Staff Verification Address': 'staffVerificationAddress',
    'Guardian Verification': 'guardianVerification',
    'Parent Verification Address': 'parentVerificationAddress',
    'PayCode Name': 'paycodeName',
    'PayCode': 'paycode',
    'Notes': 'notes',
    'Appointment ID': 'appointmentID',
    'AppointmentID': 'appointmentID',  # Alternative name
    'Validation': 'validation',
    'Place of Service': 'placeOfService'
}

def map_excel_to_db_columns(df):
    """Map Excel columns to database columns."""
    mapped_columns = {}
    
    for excel_col in df.columns:
        if excel_col in COLUMN_MAPPING:
            db_col = COLUMN_MAPPING[excel_col]
            mapped_columns[db_col] = excel_col
            print(f"✅ Mapped: '{excel_col}' → '{db_col}'")
        else:
            print(f"⚠️  Unmapped column: '{excel_col}'")
    
    return mapped_columns

def prepare_row_data(row, column_mapping):
    """Prepare a single row for database insertion."""
    # Database columns in order
    db_columns = [
        'appointmentType', 'appointmentTag', 'serviceLine', 'service',
        'appointmentLocation', 'duration', 'day', 'date', 'time',
        'scheduledDate', 'modifiedDate', 'client', 'staff', 'status',
        'sessionNote', 'staffVerification', 'staffVerificationAddress',
        'guardianVerification', 'parentVerificationAddress',
        'paycodeName', 'paycode', 'notes', 'appointmentID',
        'validation', 'placeOfService'
    ]
    
    values = []
    for db_col in db_columns:
        if db_col in column_mapping:
            excel_col = column_mapping[db_col]
            value = row[excel_col]
            
            # Handle NaN/NaT values
            if pd.isna(value):
                values.append(None)
            else:
                values.append(value)
        else:
            # Column not found in Excel, use None
            values.append(None)
    
    return values

# Map columns
print("\n🔄 Mapping Excel columns to database schema...")
column_mapping = map_excel_to_db_columns(df)

# Check if we have the required appointmentID column
if 'appointmentID' not in column_mapping:
    raise ValueError("❌ Required column 'appointmentID' not found in Excel file")

print(f"\n📊 Processing {len(df)} rows...")

# Insert data
success_count = 0
error_count = 0

with conn.cursor() as cur:
    for index, row in df.iterrows():
        try:
            values = prepare_row_data(row, column_mapping)
            
            cur.execute("""
                INSERT INTO rethinkDump (
                    appointmentType, appointmentTag, serviceLine, service,
                    appointmentLocation, duration, day, date, time,
                    scheduledDate, modifiedDate, client, staff, status,
                    sessionNote, staffVerification, staffVerificationAddress,
                    guardianVerification, parentVerificationAddress,
                    paycodeName, paycode, notes, appointmentID,
                    validation, placeOfService
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s
                )
            """, values)
            
            success_count += 1
            
            if (index + 1) % 50 == 0:
                print(f"📈 Processed {index + 1}/{len(df)} rows...")
                
        except Exception as e:
            error_count += 1
            print(f"❌ Error processing row {index + 1}: {e}")
            print(f"   Row data: {row.to_dict()}")
            
            # Continue with other rows
            continue

conn.commit()
conn.close()

print(f"\n🎉 Data upload completed!")
print(f"✅ Successfully inserted: {success_count} rows")
if error_count > 0:
    print(f"❌ Errors encountered: {error_count} rows")
print(f"📊 Total processed: {len(df)} rows")
